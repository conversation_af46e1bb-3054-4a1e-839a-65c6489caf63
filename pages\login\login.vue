<template>
  <view class="login-container">
    <!-- 页面头部区域 -->
    <view class="login-header">
      <image class="logo" src="/static/logo.png" mode="aspectFit" />
      <text class="app-name">{{ appName }}</text>
      <text class="app-desc">{{ appDescription }}</text>
    </view>

    <!-- 登录表单区域 -->
    <view class="login-form">
      <!-- 用户协议确认 -->
      <view class="agreement-section">
        <checkbox-group @change="onAgreementChange">
          <label class="agreement-item">
            <checkbox :checked="agreedToTerms" color="#4caf50" />
            <view class="agreement-text">
              <text>我已阅读并同意</text>
              <text class="agreement-link" @tap="showUserAgreement">《用户服务协议》</text>
              <text>和</text>
              <text class="agreement-link" @tap="showPrivacyPolicy">《隐私政策》</text>
            </view>
          </label>
        </checkbox-group>
      </view>

      <!-- 微信登录按钮 -->
      <uv-button
        type="primary"
        :disabled="!agreedToTerms || isLoading"
        :loading="isLoading"
        loadingText="登录中..."
        @click="handleWxLogin"
        :customStyle="loginButtonStyle"
      >
        <uv-icon name="weixin" color="#1976d2" size="40" style="margin-right: 16rpx;" />
        微信授权登录
      </uv-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { storeToRefs } from 'pinia';
import { useUserStore } from '@/src/stores/modules/user';
import { wxLogin } from '@/src/api/modules/user';
import type { LoginParams, UserInfo } from '@/src/types/api';

// ==================== Store ====================
const userStore = useUserStore();
const { profile } = storeToRefs(userStore);
const { setProfile } = userStore;

// ==================== 响应式数据 ====================
/** 是否同意用户协议 */
const agreedToTerms = ref<boolean>(false);
/** 登录加载状态 */
const isLoading = ref<boolean>(false);

// ==================== 计算属性 ====================
/** 应用名称 */
const appName = computed(() => '疾控医护考试系统');
/** 应用描述 */
const appDescription = computed(() => '专业、权威、便捷的任职资格考试平台');
/** 登录按钮样式 */
const loginButtonStyle = computed(() => ({
  width: '100%',
  height: '88rpx',
  backgroundColor: '#ffffff',
  color: agreedToTerms.value ? '#1976d2' : 'rgba(25, 118, 210, 0.5)',
  fontSize: '32rpx',
  fontWeight: '500',
  borderRadius: '16rpx',
}));

// ==================== 事件处理 ====================
/**
 * 协议勾选状态变化
 * @param value 勾选状态数组
 */
function onAgreementChange(value: string[]): void {
  agreedToTerms.value = value.length > 0;
}

/**
 * 显示用户服务协议
 */
function showUserAgreement(): void {
  uni.showModal({
    title: '用户服务协议',
    content: `
1. 本系统为疾控机构专用的任职资格考试平台
2. 用户需提供真实有效的个人信息
3. 考试过程中需遵守相关规定
4. 系统会记录用户的学习和考试行为
5. 用户信息将严格保密，仅用于考试管理

详细协议内容请联系管理员获取。
    `,
    showCancel: false,
    confirmText: '我知道了',
  });
}

/**
 * 显示隐私政策
 */
function showPrivacyPolicy(): void {
  uni.showModal({
    title: '隐私政策',
    content: `
1. 我们收集的信息：微信基本信息、个人资料、考试记录
2. 信息用途：身份验证、考试管理、成绩统计
3. 信息保护：采用加密存储，严格权限控制
4. 信息共享：仅与相关机构共享必要信息
5. 用户权利：可查看、修改个人信息

详细政策内容请联系管理员获取。
    `,
    showCancel: false,
    confirmText: '我知道了',
  });
}

/**
 * 微信授权登录
 */
async function handleWxLogin(): Promise<void> {
  // 检查协议同意状态
  if (!agreedToTerms.value) {
    uni.showToast({
      title: '请先同意用户协议',
      icon: 'none',
      duration: 2000,
    });
    return;
  }

  isLoading.value = true;

  try {
    // 调用微信登录获取code
    const loginResult = await new Promise<UniApp.LoginRes>((resolve, reject) => {
      uni.login({
        provider: 'weixin',
        success: resolve,
        fail: reject,
      });
    });

    // 构造登录参数
    const loginParams: LoginParams = {
      code: loginResult.code,
    };

    // 调用后端登录接口
    const userInfo: UserInfo = await wxLogin(loginParams);

    // 保存用户信息到Store
    setProfile(userInfo);

    // 登录成功提示
    uni.showToast({
      title: '登录成功',
      icon: 'success',
      duration: 1500,
    });

    // 根据用户状态进行页面跳转
    setTimeout(() => {
      navigateByUserStatus(userInfo.status);
    }, 1500);

  } catch (error) {
    console.error('微信登录失败:', error);
    uni.showToast({
      title: '登录失败，请重试',
      icon: 'none',
      duration: 2000,
    });
  } finally {
    isLoading.value = false;
  }
}

/**
 * 根据用户状态进行页面跳转
 * @param status 用户状态
 */
function navigateByUserStatus(status: UserInfo['status']): void {
  switch (status) {
    case 'approved':
      // 已审核通过的正式用户，跳转到信息中心
      uni.reLaunch({ url: '/pages/info/info' });
      break;
    case 'pending':
      // 待审核用户，跳转到个人中心查看审核状态
      uni.reLaunch({ url: '/pages/profile/profile' });
      break;
    case 'rejected':
      // 审核未通过用户，跳转到个人中心修改资料
      uni.reLaunch({ url: '/pages/profile/profile' });
      break;
    case 'incomplete':
    default:
      // 未提交资料的新用户，跳转到注册页面
      uni.navigateTo({ url: '/pages/register/register' });
      break;
  }
}
</script>

<style lang="scss" scoped>
@import '@/src/styles/variables.scss';

/* ==================== 主容器 ==================== */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, $primary-color 0%, $primary-light 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $spacing-xl;
  position: relative;
}

/* ==================== 头部区域 ==================== */
.login-header {
  text-align: center;
  margin-bottom: $spacing-xl * 2;
  animation: fadeInUp 0.8s ease-out;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: $spacing-lg;
  border-radius: 50%;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.app-name {
  display: block;
  margin-bottom: $spacing-sm;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.app-desc {
  display: block;
  opacity: 0.9;
}

/* ==================== 表单区域 ==================== */
.login-form {
  width: 100%;
  max-width: 640rpx;
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

/* ==================== 协议区域 ==================== */
.agreement-section {
  margin-bottom: $spacing-xl;
  padding: $spacing-md;
  background: rgba(255, 255, 255, 0.1);
  border-radius: $border-radius-medium;
  backdrop-filter: blur(10rpx);
}

.agreement-text {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  line-height: $line-height-loose;
  margin-left: $spacing-sm;
}

.agreement-link {
  text-decoration: underline;
  margin: 0 4rpx;
}

/* ==================== 动画效果 ==================== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(60rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ==================== 响应式适配 ==================== */
@media screen and (max-width: 750rpx) {
  .login-container {
    padding: $spacing-lg;
  }

  .logo {
    width: 140rpx;
    height: 140rpx;
  }
}

/* ==================== 深色模式适配 ==================== */
@media (prefers-color-scheme: dark) {
  .agreement-section {
    background: rgba(0, 0, 0, 0.2);
  }
}
</style>
